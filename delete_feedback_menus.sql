-- =============================================
-- 删除反馈系统菜单及子菜单的SQL脚本
-- 创建时间: 2025-09-20
-- 说明: 删除反馈系统相关的所有菜单项和权限
-- =============================================

-- 开始事务
START TRANSACTION;

-- 临时禁用安全更新模式（如果需要）
SET SQL_SAFE_UPDATES = 0;

-- 1. 删除角色菜单关联关系（sys_role_menu表）
-- 删除反馈系统相关菜单的角色权限分配

-- 方法1：使用IN子句（如果安全模式允许）
DELETE FROM sys_role_menu WHERE menu_id IN (
    2082, 2083, 2084, 2085, 2086, 2087,
    2088, 2089, 2090, 2091, 2092, 2093, 2094, 2095, 2096,
    2104, 2105, 2106, 2107, 2108, 2109
);

-- 方法2：如果上面的方法不行，可以逐个删除
-- DELETE FROM sys_role_menu WHERE menu_id = 2082;
-- DELETE FROM sys_role_menu WHERE menu_id = 2083;
-- DELETE FROM sys_role_menu WHERE menu_id = 2084;
-- DELETE FROM sys_role_menu WHERE menu_id = 2085;
-- DELETE FROM sys_role_menu WHERE menu_id = 2086;
-- DELETE FROM sys_role_menu WHERE menu_id = 2087;
-- DELETE FROM sys_role_menu WHERE menu_id = 2088;
-- DELETE FROM sys_role_menu WHERE menu_id = 2089;
-- DELETE FROM sys_role_menu WHERE menu_id = 2090;
-- DELETE FROM sys_role_menu WHERE menu_id = 2091;
-- DELETE FROM sys_role_menu WHERE menu_id = 2092;
-- DELETE FROM sys_role_menu WHERE menu_id = 2093;
-- DELETE FROM sys_role_menu WHERE menu_id = 2094;
-- DELETE FROM sys_role_menu WHERE menu_id = 2095;
-- DELETE FROM sys_role_menu WHERE menu_id = 2096;
-- DELETE FROM sys_role_menu WHERE menu_id = 2104;
-- DELETE FROM sys_role_menu WHERE menu_id = 2105;
-- DELETE FROM sys_role_menu WHERE menu_id = 2106;
-- DELETE FROM sys_role_menu WHERE menu_id = 2107;
-- DELETE FROM sys_role_menu WHERE menu_id = 2108;
-- DELETE FROM sys_role_menu WHERE menu_id = 2109;

-- 2. 删除菜单数据（sys_menu表）
-- 按照层级顺序删除，先删除子菜单和按钮，再删除父菜单

-- 删除反馈系统按钮权限菜单（逐个删除以避免安全模式问题）
DELETE FROM sys_menu WHERE menu_id = 2088; -- 反馈提交
DELETE FROM sys_menu WHERE menu_id = 2089; -- 反馈查询
DELETE FROM sys_menu WHERE menu_id = 2090; -- 反馈详情
DELETE FROM sys_menu WHERE menu_id = 2091; -- 反馈查询
DELETE FROM sys_menu WHERE menu_id = 2092; -- 反馈详情
DELETE FROM sys_menu WHERE menu_id = 2093; -- 反馈处理
DELETE FROM sys_menu WHERE menu_id = 2094; -- 状态更新
DELETE FROM sys_menu WHERE menu_id = 2095; -- 反馈删除
DELETE FROM sys_menu WHERE menu_id = 2096; -- 反馈导出
DELETE FROM sys_menu WHERE menu_id = 2104; -- 优化查询
DELETE FROM sys_menu WHERE menu_id = 2105; -- 优化审核
DELETE FROM sys_menu WHERE menu_id = 2106; -- 优化实施
DELETE FROM sys_menu WHERE menu_id = 2107; -- 优化统计
DELETE FROM sys_menu WHERE menu_id = 2108; -- 统计查看
DELETE FROM sys_menu WHERE menu_id = 2109; -- 统计导出

-- 删除反馈系统子菜单
DELETE FROM sys_menu WHERE menu_id = 2083; -- 用户反馈
DELETE FROM sys_menu WHERE menu_id = 2084; -- 反馈管理
DELETE FROM sys_menu WHERE menu_id = 2085; -- 反馈分类
DELETE FROM sys_menu WHERE menu_id = 2086; -- 知识库优化
DELETE FROM sys_menu WHERE menu_id = 2087; -- 反馈统计

-- 删除反馈系统主菜单
DELETE FROM sys_menu WHERE menu_id = 2082; -- 反馈系统

-- 3. 验证删除结果
-- 查询是否还有反馈系统相关的菜单残留
SELECT 
    menu_id, 
    menu_name, 
    parent_id, 
    path, 
    perms 
FROM sys_menu 
WHERE 
    menu_id BETWEEN 2082 AND 2109 
    OR menu_name LIKE '%反馈%' 
    OR path LIKE '%feedback%' 
    OR perms LIKE '%feedback%'
ORDER BY menu_id;

-- 查询是否还有相关的角色菜单关联
SELECT 
    rm.role_id, 
    rm.menu_id, 
    r.role_name 
FROM sys_role_menu rm 
LEFT JOIN sys_role r ON rm.role_id = r.role_id 
WHERE rm.menu_id BETWEEN 2082 AND 2109;

-- 4. 如果验证结果为空，则提交事务；否则需要回滚
-- 注意：请在执行前备份数据库，并在测试环境中先行验证

-- 恢复安全更新模式
SET SQL_SAFE_UPDATES = 1;

-- 提交事务（如果一切正常）
COMMIT;

-- 如果需要回滚，请执行以下命令：
-- ROLLBACK;

-- =============================================
-- 执行说明：
-- 1. 请在执行前备份数据库
-- 2. 建议先在测试环境中执行验证
-- 3. 执行后检查验证查询的结果
-- 4. 如果验证查询返回空结果，说明删除成功
-- 5. 如果发现问题，可以使用 ROLLBACK 回滚事务
-- =============================================
