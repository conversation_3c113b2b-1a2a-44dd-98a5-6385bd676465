-- =============================================
-- 删除反馈系统菜单及子菜单的SQL脚本（安全模式兼容版本）
-- 创建时间: 2025-09-20
-- 说明: 兼容MySQL安全更新模式的删除脚本
-- =============================================

-- 开始事务
START TRANSACTION;

-- 1. 删除角色菜单关联关系（逐个删除）
DELETE FROM sys_role_menu WHERE menu_id = 2082;
DELETE FROM sys_role_menu WHERE menu_id = 2083;
DELETE FROM sys_role_menu WHERE menu_id = 2084;
DELETE FROM sys_role_menu WHERE menu_id = 2085;
DELETE FROM sys_role_menu WHERE menu_id = 2086;
DELETE FROM sys_role_menu WHERE menu_id = 2087;
DELETE FROM sys_role_menu WHERE menu_id = 2088;
DELETE FROM sys_role_menu WHERE menu_id = 2089;
DELETE FROM sys_role_menu WHERE menu_id = 2090;
DELETE FROM sys_role_menu WHERE menu_id = 2091;
DELETE FROM sys_role_menu WHERE menu_id = 2092;
DELETE FROM sys_role_menu WHERE menu_id = 2093;
DELETE FROM sys_role_menu WHERE menu_id = 2094;
DELETE FROM sys_role_menu WHERE menu_id = 2095;
DELETE FROM sys_role_menu WHERE menu_id = 2096;
DELETE FROM sys_role_menu WHERE menu_id = 2104;
DELETE FROM sys_role_menu WHERE menu_id = 2105;
DELETE FROM sys_role_menu WHERE menu_id = 2106;
DELETE FROM sys_role_menu WHERE menu_id = 2107;
DELETE FROM sys_role_menu WHERE menu_id = 2108;
DELETE FROM sys_role_menu WHERE menu_id = 2109;

-- 2. 删除菜单数据（按层级顺序，先删除按钮权限）
DELETE FROM sys_menu WHERE menu_id = 2088; -- 反馈提交
DELETE FROM sys_menu WHERE menu_id = 2089; -- 反馈查询
DELETE FROM sys_menu WHERE menu_id = 2090; -- 反馈详情
DELETE FROM sys_menu WHERE menu_id = 2091; -- 反馈查询
DELETE FROM sys_menu WHERE menu_id = 2092; -- 反馈详情
DELETE FROM sys_menu WHERE menu_id = 2093; -- 反馈处理
DELETE FROM sys_menu WHERE menu_id = 2094; -- 状态更新
DELETE FROM sys_menu WHERE menu_id = 2095; -- 反馈删除
DELETE FROM sys_menu WHERE menu_id = 2096; -- 反馈导出
DELETE FROM sys_menu WHERE menu_id = 2104; -- 优化查询
DELETE FROM sys_menu WHERE menu_id = 2105; -- 优化审核
DELETE FROM sys_menu WHERE menu_id = 2106; -- 优化实施
DELETE FROM sys_menu WHERE menu_id = 2107; -- 优化统计
DELETE FROM sys_menu WHERE menu_id = 2108; -- 统计查看
DELETE FROM sys_menu WHERE menu_id = 2109; -- 统计导出

-- 3. 删除子菜单
DELETE FROM sys_menu WHERE menu_id = 2083; -- 用户反馈
DELETE FROM sys_menu WHERE menu_id = 2084; -- 反馈管理
DELETE FROM sys_menu WHERE menu_id = 2085; -- 反馈分类
DELETE FROM sys_menu WHERE menu_id = 2086; -- 知识库优化
DELETE FROM sys_menu WHERE menu_id = 2087; -- 反馈统计

-- 4. 删除主菜单
DELETE FROM sys_menu WHERE menu_id = 2082; -- 反馈系统

-- 5. 验证删除结果
SELECT '=== 验证删除结果 ===' as message;

SELECT 
    menu_id, 
    menu_name, 
    parent_id, 
    path, 
    perms 
FROM sys_menu 
WHERE 
    menu_id BETWEEN 2082 AND 2109 
    OR menu_name LIKE '%反馈%' 
    OR path LIKE '%feedback%' 
    OR perms LIKE '%feedback%'
ORDER BY menu_id;

SELECT 
    rm.role_id, 
    rm.menu_id, 
    r.role_name 
FROM sys_role_menu rm 
LEFT JOIN sys_role r ON rm.role_id = r.role_id 
WHERE rm.menu_id BETWEEN 2082 AND 2109;

-- 6. 提交事务
COMMIT;

-- =============================================
-- 执行说明：
-- 1. 此脚本兼容MySQL安全更新模式
-- 2. 使用逐个删除的方式避免安全模式限制
-- 3. 请在执行前备份数据库
-- 4. 执行后检查验证查询的结果
-- 5. 如果验证查询返回空结果，说明删除成功
-- =============================================
